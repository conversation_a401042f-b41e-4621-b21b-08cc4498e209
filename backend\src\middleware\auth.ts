import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import { appConfig } from '@/config';
import { JwtPayload, AppError } from '@/types';
import { logger } from '@/utils/logger';
import { redisService } from '@/services/redis-client';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
      userEmails?: string[];
    }
  }
}

/**
 * JWT认证中间件
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AppError('Access token required', 401);
    }

    // 验证JWT token
    const decoded = jwt.verify(token, appConfig.jwtSecret) as JwtPayload;

    // 检查会话是否存在
    const sessionData = await redisService.getSession(token);
    if (!sessionData) {
      throw new AppError('Invalid or expired session', 401);
    }

    // 获取用户邮箱权限
    const userEmails = await redisService.getUserEmails(decoded.userId);

    // 将用户信息添加到请求对象
    req.user = decoded;
    req.userEmails = userEmails;

    logger.debug(`User authenticated: ${decoded.username} (${decoded.userId})`);
    next();

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid JWT token:', error.message);
      res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    } else if (error instanceof AppError) {
      logger.warn('Authentication error:', error.message);
      res.status(error.statusCode).json({
        success: false,
        error: error.message
      });
    } else {
      logger.error('Authentication middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
};

/**
 * 邮箱权限验证中间件
 */
export const requireEmailPermission = (emailParam = 'email') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AppError('Authentication required', 401);
      }

      const email = req.params[emailParam] || req.body[emailParam] || req.query[emailParam];
      
      if (!email) {
        throw new AppError(`Email parameter '${emailParam}' is required`, 400);
      }

      // 检查用户是否有权限访问该邮箱
      const hasPermission = await redisService.hasEmailPermission(req.user.userId, email);
      
      if (!hasPermission) {
        logger.warn(`User ${req.user.userId} attempted to access unauthorized email: ${email}`);
        throw new AppError('No permission to access this email', 403);
      }

      logger.debug(`Email permission granted for ${req.user.userId} -> ${email}`);
      next();

    } catch (error) {
      if (error instanceof AppError) {
        res.status(error.statusCode).json({
          success: false,
          error: error.message
        });
      } else {
        logger.error('Email permission middleware error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error'
        });
      }
    }
  };
};

/**
 * 可选认证中间件（用于公开接口但需要用户信息的场景）
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      try {
        const decoded = jwt.verify(token, appConfig.jwtSecret) as JwtPayload;
        const sessionData = await redisService.getSession(token);
        
        if (sessionData) {
          const userEmails = await redisService.getUserEmails(decoded.userId);
          req.user = decoded;
          req.userEmails = userEmails;
        }
      } catch (error) {
        // 忽略token验证错误，继续处理请求
        logger.debug('Optional auth failed, continuing without user info');
      }
    }

    next();
  } catch (error) {
    logger.error('Optional auth middleware error:', error);
    next(); // 继续处理请求
  }
};

/**
 * 管理员权限验证中间件
 */
export const requireAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      throw new AppError('Authentication required', 401);
    }

    // 这里可以添加管理员权限检查逻辑
    // 例如检查用户角色或特定权限
    const isAdmin = req.user.username === 'admin' || req.userEmails?.includes('<EMAIL>');
    
    if (!isAdmin) {
      logger.warn(`User ${req.user.userId} attempted to access admin endpoint`);
      throw new AppError('Admin permission required', 403);
    }

    next();
  } catch (error) {
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        error: error.message
      });
    } else {
      logger.error('Admin permission middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
};

/**
 * 速率限制中间件
 */
export const rateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    const identifier = req.ip || 'unknown';
    const now = Date.now();
    
    const userRequests = requests.get(identifier);
    
    if (!userRequests || now > userRequests.resetTime) {
      // 重置或初始化计数器
      requests.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      });
      next();
      return;
    }

    if (userRequests.count >= maxRequests) {
      logger.warn(`Rate limit exceeded for ${identifier}`);
      res.status(429).json({
        success: false,
        error: 'Too many requests',
        retryAfter: Math.ceil((userRequests.resetTime - now) / 1000)
      });
      return;
    }

    userRequests.count++;
    next();
  };
};

/**
 * 错误处理中间件
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  logger.error('Unhandled error:', error);

  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      success: false,
      error: error.message
    });
  } else if (error instanceof jwt.JsonWebTokenError) {
    res.status(401).json({
      success: false,
      error: 'Invalid token'
    });
  } else {
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

/**
 * 请求日志中间件
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const userId = req.user?.userId || 'anonymous';
    
    logger.info(`${req.method} ${req.path}`, {
      userId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  });

  next();
};
