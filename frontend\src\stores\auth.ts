import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'
import type { LoginRequest, LoginResponse, User } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const user = ref<User | null>(null)
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userEmails = computed(() => user.value?.emails || [])

  // 初始化认证状态
  const initializeAuth = async () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedRefreshToken = localStorage.getItem('refresh_token')
    const savedUser = localStorage.getItem('user_info')

    if (savedToken && savedUser) {
      token.value = savedToken
      refreshToken.value = savedRefreshToken || ''
      user.value = JSON.parse(savedUser)

      try {
        // 验证token有效性
        await verifyToken()
      } catch (error) {
        // Token无效，清除本地存储
        await logout()
      }
    }
  }

  // 登录
  const login = async (loginData: LoginRequest): Promise<void> => {
    loading.value = true
    try {
      const response = await authApi.login(loginData)
      
      if (response.success && response.data) {
        const { token: accessToken, refreshToken: newRefreshToken, user: userData } = response.data
        
        // 更新状态
        token.value = accessToken
        refreshToken.value = newRefreshToken
        user.value = userData

        // 保存到本地存储
        localStorage.setItem('auth_token', accessToken)
        localStorage.setItem('refresh_token', newRefreshToken)
        localStorage.setItem('user_info', JSON.stringify(userData))

        ElMessage.success('登录成功')
      } else {
        throw new Error(response.error || '登录失败')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.warn('Logout API call failed:', error)
    } finally {
      // 清除状态
      token.value = ''
      refreshToken.value = ''
      user.value = null

      // 清除本地存储
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user_info')

      ElMessage.success('已退出登录')
    }
  }

  // 刷新token
  const refreshAccessToken = async (): Promise<void> => {
    if (!refreshToken.value) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authApi.refreshToken(refreshToken.value)
      
      if (response.success && response.data) {
        const { token: newToken } = response.data
        
        token.value = newToken
        localStorage.setItem('auth_token', newToken)
      } else {
        throw new Error(response.error || 'Token refresh failed')
      }
    } catch (error) {
      // 刷新失败，清除认证状态
      await logout()
      throw error
    }
  }

  // 验证token
  const verifyToken = async (): Promise<void> => {
    if (!token.value) {
      throw new Error('No token available')
    }

    try {
      const response = await authApi.verifyToken()
      
      if (!response.success) {
        throw new Error(response.error || 'Token verification failed')
      }
    } catch (error: any) {
      if (error.response?.status === 401) {
        // Token过期，尝试刷新
        try {
          await refreshAccessToken()
        } catch (refreshError) {
          throw refreshError
        }
      } else {
        throw error
      }
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (): Promise<void> => {
    try {
      const response = await authApi.getUserInfo()
      
      if (response.success && response.data) {
        user.value = response.data
        localStorage.setItem('user_info', JSON.stringify(response.data))
      } else {
        throw new Error(response.error || 'Failed to fetch user info')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取用户信息失败')
      throw error
    }
  }

  // 更新用户邮箱
  const updateUserEmails = async (emails: string[]): Promise<void> => {
    try {
      const response = await authApi.updateEmails(emails)
      
      if (response.success) {
        // 更新本地用户信息
        if (user.value) {
          user.value.emails = emails
          localStorage.setItem('user_info', JSON.stringify(user.value))
        }
        
        ElMessage.success('邮箱权限更新成功')
      } else {
        throw new Error(response.error || 'Failed to update emails')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '更新邮箱权限失败')
      throw error
    }
  }

  // 检查邮箱权限
  const hasEmailPermission = (email: string): boolean => {
    return userEmails.value.includes(email.toLowerCase())
  }

  // 获取认证头
  const getAuthHeader = (): string => {
    return token.value ? `Bearer ${token.value}` : ''
  }

  return {
    // 状态
    token: readonly(token),
    refreshToken: readonly(refreshToken),
    user: readonly(user),
    loading: readonly(loading),
    
    // 计算属性
    isAuthenticated,
    userEmails,
    
    // 方法
    initializeAuth,
    login,
    logout,
    refreshAccessToken,
    verifyToken,
    fetchUserInfo,
    updateUserEmails,
    hasEmailPermission,
    getAuthHeader
  }
})
