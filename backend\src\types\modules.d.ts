// 第三方模块类型声明

declare module 'mailparser' {
  export interface ParsedMail {
    subject?: string;
    from?: {
      text: string;
      value: Array<{
        address: string;
        name: string;
      }>;
    };
    to?: {
      text: string;
      value: Array<{
        address: string;
        name: string;
      }>;
    };
    date?: Date;
    text?: string;
    html?: string;
    attachments?: Array<{
      filename?: string;
      contentType: string;
      content: Buffer;
    }>;
  }

  export function simpleParser(source: string | Buffer): Promise<ParsedMail>;
}

declare module 'imap' {
  interface ImapConfig {
    user: string;
    password: string;
    host: string;
    port: number;
    tls: boolean;
    tlsOptions?: {
      rejectUnauthorized: boolean;
    };
    keepalive?: boolean;
    connTimeout?: number;
    authTimeout?: number;
  }

  interface Box {
    name: string;
    messages: {
      total: number;
      new: number;
    };
  }

  interface ImapMessage {
    on(event: 'body', listener: (stream: NodeJS.ReadableStream) => void): void;
    on(event: 'attributes', listener: (attrs: any) => void): void;
    once(event: 'end', listener: () => void): void;
  }

  interface ImapFetch {
    on(event: 'message', listener: (msg: ImapMessage, seqno: number) => void): void;
    once(event: 'error', listener: (err: Error) => void): void;
    once(event: 'end', listener: () => void): void;
  }

  class Connection {
    constructor(config: ImapConfig);
    
    connect(): void;
    end(): void;
    
    openBox(name: string, readOnly: boolean, callback: (err: Error | null, box?: Box) => void): void;
    search(criteria: string[], callback: (err: Error | null, results?: number[]) => void): void;
    fetch(source: number[], options: any): ImapFetch;
    
    once(event: 'ready', listener: () => void): void;
    once(event: 'error', listener: (err: Error) => void): void;
    once(event: 'end', listener: () => void): void;
    on(event: 'mail', listener: (numNewMsgs: number) => void): void;
    on(event: 'expunge', listener: (seqno: number) => void): void;
  }

  export = Connection;
}
