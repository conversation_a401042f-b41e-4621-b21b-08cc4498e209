import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'
import { codesApi } from '@/api/codes'
import { wsService } from '@/services/websocket'
import { useAuthStore } from './auth'
import type { VerificationCode, CodeOverview, CodeStats } from '@/types'

export const useCodesStore = defineStore('codes', () => {
  // 状态
  const codes = ref<Map<string, VerificationCode[]>>(new Map())
  const overview = ref<CodeOverview[]>([])
  const stats = ref<CodeStats | null>(null)
  const loading = ref(false)
  const wsConnected = ref(false)

  // 计算属性
  const totalEmails = computed(() => overview.value.length)
  const activeEmails = computed(() => overview.value.filter(item => item.hasActiveCodes).length)
  const totalCodes = computed(() => overview.value.reduce((sum, item) => sum + item.totalCodes, 0))

  // 获取指定邮箱的验证码
  const getEmailCodes = (email: string): VerificationCode[] => {
    return codes.value.get(email) || []
  }

  // 获取指定邮箱的最新验证码
  const getLatestCode = (email: string): VerificationCode | null => {
    const emailCodes = getEmailCodes(email)
    return emailCodes.length > 0 ? emailCodes[0] : null
  }

  // 初始化WebSocket连接
  const initWebSocket = async (): Promise<void> => {
    const authStore = useAuthStore()
    
    if (!authStore.isAuthenticated) {
      return
    }

    try {
      await wsService.connect()
      wsConnected.value = true

      // 设置事件监听器
      wsService.on('connected', (data) => {
        console.log('WebSocket connected:', data)
        // 为所有邮箱加入房间
        authStore.userEmails.forEach(email => {
          wsService.joinEmailRoom(email)
        })
      })

      wsService.on('new-code', (code) => {
        addCode(code)
        updateOverview()
      })

      wsService.on('code-expired', (data) => {
        markCodeExpired(data.email)
      })

      wsService.on('latest-code', (data) => {
        if (data.code) {
          updateLatestCode(data.email, data.code)
        }
      })

      wsService.on('disconnect', () => {
        wsConnected.value = false
      })

    } catch (error) {
      console.error('Failed to initialize WebSocket:', error)
      wsConnected.value = false
    }
  }

  // 断开WebSocket连接
  const disconnectWebSocket = (): void => {
    wsService.disconnect()
    wsConnected.value = false
  }

  // 获取验证码概览
  const fetchOverview = async (): Promise<void> => {
    loading.value = true
    try {
      const response = await codesApi.getOverview()
      if (response.success && response.data) {
        overview.value = response.data.overview
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取概览失败')
    } finally {
      loading.value = false
    }
  }

  // 获取指定邮箱的验证码
  const fetchCodes = async (email: string, limit = 10): Promise<void> => {
    try {
      const response = await codesApi.getCodes(email, { limit })
      if (response.success && response.data) {
        codes.value.set(email, response.data.codes)
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取验证码失败')
    }
  }

  // 获取最新验证码
  const fetchLatestCode = async (email: string): Promise<VerificationCode | null> => {
    try {
      const response = await codesApi.getLatestCode(email)
      if (response.success) {
        const code = response.data
        if (code) {
          updateLatestCode(email, code)
        }
        return code
      }
      return null
    } catch (error: any) {
      ElMessage.error(error.message || '获取最新验证码失败')
      return null
    }
  }

  // 删除验证码
  const deleteCodes = async (email: string): Promise<void> => {
    try {
      const response = await codesApi.deleteCodes(email)
      if (response.success) {
        codes.value.delete(email)
        updateOverview()
        ElMessage.success('验证码已清除')
      }
    } catch (error: any) {
      ElMessage.error(error.message || '删除验证码失败')
    }
  }

  // 获取统计信息
  const fetchStats = async (): Promise<void> => {
    try {
      const response = await codesApi.getStats()
      if (response.success && response.data) {
        stats.value = response.data
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取统计信息失败')
    }
  }

  // 添加新验证码
  const addCode = (code: VerificationCode): void => {
    const emailCodes = codes.value.get(code.email) || []
    
    // 检查是否已存在相同的验证码
    const exists = emailCodes.some(c => 
      c.code === code.code && 
      c.receivedAt === code.receivedAt
    )
    
    if (!exists) {
      // 添加到列表开头
      emailCodes.unshift(code)
      
      // 限制保存的验证码数量
      if (emailCodes.length > 50) {
        emailCodes.splice(50)
      }
      
      codes.value.set(code.email, emailCodes)
    }
  }

  // 更新最新验证码
  const updateLatestCode = (email: string, code: VerificationCode): void => {
    const emailCodes = codes.value.get(email) || []
    
    // 如果是新的验证码，添加到开头
    if (emailCodes.length === 0 || emailCodes[0].receivedAt < code.receivedAt) {
      emailCodes.unshift(code)
      codes.value.set(email, emailCodes)
    }
  }

  // 标记验证码过期
  const markCodeExpired = (email: string): void => {
    const emailCodes = codes.value.get(email)
    if (emailCodes) {
      emailCodes.forEach(code => {
        if ((Date.now() - code.receivedAt) > 120000) {
          code.isExpired = true
        }
      })
    }
  }

  // 更新概览数据
  const updateOverview = (): void => {
    const authStore = useAuthStore()
    const newOverview: CodeOverview[] = []

    authStore.userEmails.forEach(email => {
      const emailCodes = getEmailCodes(email)
      const latestCode = getLatestCode(email)
      const activeCodes = emailCodes.filter(code => 
        (Date.now() - code.receivedAt) <= 120000
      )

      newOverview.push({
        email,
        latestCode: latestCode ? {
          ...latestCode,
          isExpired: latestCode ? (Date.now() - latestCode.receivedAt) > 120000 : false,
          ageInSeconds: latestCode ? Math.floor((Date.now() - latestCode.receivedAt) / 1000) : 0
        } : null,
        totalCodes: emailCodes.length,
        hasActiveCodes: activeCodes.length > 0
      })
    })

    overview.value = newOverview
  }

  // 加入邮箱房间
  const joinEmailRoom = (email: string): void => {
    if (wsConnected.value) {
      wsService.joinEmailRoom(email)
    }
  }

  // 离开邮箱房间
  const leaveEmailRoom = (email: string): void => {
    if (wsConnected.value) {
      wsService.leaveEmailRoom(email)
    }
  }

  // 请求最新验证码（通过WebSocket）
  const requestLatestCode = (email: string): void => {
    if (wsConnected.value) {
      wsService.getLatestCode(email)
    }
  }

  // 请求验证码历史（通过WebSocket）
  const requestCodeHistory = (email: string, limit = 10): void => {
    if (wsConnected.value) {
      wsService.getCodeHistory(email, limit)
    }
  }

  // 清理过期验证码
  const cleanupExpiredCodes = (): void => {
    const now = Date.now()
    
    codes.value.forEach((emailCodes, email) => {
      emailCodes.forEach(code => {
        code.isExpired = (now - code.receivedAt) > 120000
        code.ageInSeconds = Math.floor((now - code.receivedAt) / 1000)
      })
    })
    
    updateOverview()
  }

  // 定期清理过期验证码
  const startCleanupTimer = (): void => {
    setInterval(cleanupExpiredCodes, 10000) // 每10秒清理一次
  }

  return {
    // 状态
    codes: readonly(codes),
    overview: readonly(overview),
    stats: readonly(stats),
    loading: readonly(loading),
    wsConnected: readonly(wsConnected),
    
    // 计算属性
    totalEmails,
    activeEmails,
    totalCodes,
    
    // 方法
    getEmailCodes,
    getLatestCode,
    initWebSocket,
    disconnectWebSocket,
    fetchOverview,
    fetchCodes,
    fetchLatestCode,
    deleteCodes,
    fetchStats,
    addCode,
    updateLatestCode,
    markCodeExpired,
    updateOverview,
    joinEmailRoom,
    leaveEmailRoom,
    requestLatestCode,
    requestCodeHistory,
    cleanupExpiredCodes,
    startCleanupTimer
  }
})
