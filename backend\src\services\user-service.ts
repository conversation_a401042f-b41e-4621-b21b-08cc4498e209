import bcrypt from 'bcryptjs';
import jwt, { SignOptions } from 'jsonwebtoken';
import { appConfig } from '@/config';
import { User, JwtPayload, LoginRequest, LoginResponse, AppError } from '@/types';
import { logger } from '@/utils/logger';
import { redisService } from './redis-client';

// 模拟用户数据库（实际项目中应该使用真实数据库）
const USERS_DB = new Map<string, User>();
const PASSWORDS_DB = new Map<string, string>(); // 单独存储密码

// 初始化默认用户（仅用于演示）
const initializeDefaultUsers = async (): Promise<void> => {
  const defaultUsers = [
    {
      id: 'user1',
      username: 'admin',
      password: 'admin123',
      emails: ['<EMAIL>', '<EMAIL>']
    },
    {
      id: 'user2',
      username: 'user1',
      password: 'user123',
      emails: ['<EMAIL>', '<EMAIL>']
    }
  ];

  for (const userData of defaultUsers) {
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    
    const user: User = {
      id: userData.id,
      username: userData.username,
      emails: userData.emails,
      createdAt: Date.now()
    };

    USERS_DB.set(userData.username, user);

    // 存储密码（实际项目中应该使用真实数据库）
    PASSWORDS_DB.set(userData.username, hashedPassword);
    
    // 存储用户邮箱权限到Redis
    await redisService.storeUserEmails(user.id, user.emails);
    
    logger.info(`Initialized user: ${user.username} with emails: ${user.emails.join(', ')}`);
  }
};

export class UserService {
  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      await initializeDefaultUsers();
      logger.info('User service initialized');
    } catch (error) {
      logger.error('Failed to initialize user service:', error);
    }
  }

  /**
   * 用户登录
   */
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      const { username, password } = loginData;

      // 查找用户
      const user = USERS_DB.get(username);
      if (!user) {
        throw new AppError('Invalid username or password', 401);
      }

      // 验证密码
      const storedPassword = PASSWORDS_DB.get(username);
      if (!storedPassword) {
        throw new AppError('Invalid username or password', 401);
      }

      const isPasswordValid = await bcrypt.compare(password, storedPassword);
      
      if (!isPasswordValid) {
        throw new AppError('Invalid username or password', 401);
      }

      // 生成JWT token
      const tokenPayload: JwtPayload = {
        userId: user.id,
        username: user.username,
        emails: user.emails
      };

      const token = jwt.sign(tokenPayload, appConfig.jwtSecret, {
        expiresIn: appConfig.jwtExpiresIn as any
      });

      const refreshToken = jwt.sign(
        { userId: user.id, type: 'refresh' },
        appConfig.jwtSecret,
        { expiresIn: appConfig.jwtRefreshExpiresIn as any }
      );

      // 计算过期时间
      const expiresIn = this.parseExpirationTime(appConfig.jwtExpiresIn as string);

      // 存储会话到Redis
      await redisService.storeSession(token, {
        userId: user.id,
        username: user.username,
        emails: user.emails,
        loginAt: Date.now()
      }, expiresIn);

      // 更新最后登录时间
      user.lastLoginAt = Date.now();
      USERS_DB.set(username, user);

      logger.info(`User logged in: ${username} (${user.id})`);

      return {
        token,
        refreshToken,
        user: {
          username: user.username,
          emails: user.emails,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        },
        expiresIn
      };

    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Login error:', error);
      throw new AppError('Login failed', 500);
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(refreshToken: string): Promise<{ token: string; expiresIn: number }> {
    try {
      // 验证refresh token
      const decoded = jwt.verify(refreshToken, appConfig.jwtSecret) as { userId: string; type: string };
      
      if (decoded.type !== 'refresh') {
        throw new AppError('Invalid refresh token', 401);
      }

      // 查找用户
      const user = Array.from(USERS_DB.values()).find(u => u.id === decoded.userId);
      if (!user) {
        throw new AppError('User not found', 404);
      }

      // 生成新的access token
      const tokenPayload: JwtPayload = {
        userId: user.id,
        username: user.username,
        emails: user.emails
      };

      const newToken = jwt.sign(tokenPayload, appConfig.jwtSecret, {
        expiresIn: appConfig.jwtExpiresIn as any
      });

      const expiresIn = this.parseExpirationTime(appConfig.jwtExpiresIn as string);

      // 存储新会话
      await redisService.storeSession(newToken, {
        userId: user.id,
        username: user.username,
        emails: user.emails,
        refreshedAt: Date.now()
      }, expiresIn);

      logger.info(`Token refreshed for user: ${user.username}`);

      return {
        token: newToken,
        expiresIn
      };

    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AppError('Invalid refresh token', 401);
      }
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Token refresh error:', error);
      throw new AppError('Token refresh failed', 500);
    }
  }

  /**
   * 用户登出
   */
  async logout(token: string): Promise<void> {
    try {
      // 删除会话
      await redisService.deleteSession(token);
      
      logger.info('User logged out');
    } catch (error) {
      logger.error('Logout error:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(userId: string): Promise<Omit<User, 'id'> | null> {
    try {
      const user = Array.from(USERS_DB.values()).find(u => u.id === userId);
      
      if (!user) {
        return null;
      }

      return {
        username: user.username,
        emails: user.emails,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt || 0
      };
    } catch (error) {
      logger.error('Get user info error:', error);
      throw new AppError('Failed to get user info', 500);
    }
  }

  /**
   * 更新用户邮箱权限
   */
  async updateUserEmails(userId: string, emails: string[]): Promise<void> {
    try {
      const user = Array.from(USERS_DB.values()).find(u => u.id === userId);
      
      if (!user) {
        throw new AppError('User not found', 404);
      }

      // 更新用户邮箱列表
      user.emails = emails;
      USERS_DB.set(user.username, user);

      // 更新Redis中的邮箱权限
      await redisService.storeUserEmails(userId, emails);

      logger.info(`Updated emails for user ${userId}: ${emails.join(', ')}`);
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Update user emails error:', error);
      throw new AppError('Failed to update user emails', 500);
    }
  }

  /**
   * 验证用户邮箱权限
   */
  async hasEmailPermission(userId: string, email: string): Promise<boolean> {
    try {
      return await redisService.hasEmailPermission(userId, email);
    } catch (error) {
      logger.error('Check email permission error:', error);
      return false;
    }
  }

  /**
   * 获取用户邮箱列表
   */
  async getUserEmails(userId: string): Promise<string[]> {
    try {
      return await redisService.getUserEmails(userId);
    } catch (error) {
      logger.error('Get user emails error:', error);
      return [];
    }
  }

  /**
   * 创建新用户（管理员功能）
   */
  async createUser(userData: {
    username: string;
    password: string;
    emails: string[];
  }): Promise<User> {
    try {
      // 检查用户名是否已存在
      if (USERS_DB.has(userData.username)) {
        throw new AppError('Username already exists', 409);
      }

      // 生成用户ID
      const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 加密密码
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // 创建用户
      const user: User = {
        id: userId,
        username: userData.username,
        emails: userData.emails,
        createdAt: Date.now()
      };

      // 存储用户
      USERS_DB.set(userData.username, user);
      PASSWORDS_DB.set(userData.username, hashedPassword);

      // 存储邮箱权限
      await redisService.storeUserEmails(userId, userData.emails);

      logger.info(`Created new user: ${userData.username} with emails: ${userData.emails.join(', ')}`);

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      logger.error('Create user error:', error);
      throw new AppError('Failed to create user', 500);
    }
  }

  /**
   * 解析过期时间字符串为秒数
   */
  private parseExpirationTime(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 24 * 60 * 60; // 默认24小时
    }

    const value = parseInt(match[1]!, 10);
    const unit = match[2]!;

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: return 24 * 60 * 60;
    }
  }

  /**
   * 获取所有用户（管理员功能）
   */
  getAllUsers(): Array<Omit<User, 'id'>> {
    return Array.from(USERS_DB.values())
      .map(user => ({
        username: user.username,
        emails: user.emails,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt || 0
      }));
  }
}

// 创建单例实例
export const userService = new UserService();
